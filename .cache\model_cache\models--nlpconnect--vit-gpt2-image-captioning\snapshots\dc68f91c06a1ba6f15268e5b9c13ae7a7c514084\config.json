{"_name_or_path": "vit-gpt-pt", "architectures": ["VisionEncoderDecoderModel"], "bos_token_id": 50256, "decoder": {"_name_or_path": "", "activation_function": "gelu_new", "add_cross_attention": true, "architectures": ["GPT2LMHeadModel"], "attn_pdrop": 0.1, "bad_words_ids": null, "bos_token_id": 50256, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": 50256, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "embd_pdrop": 0.1, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 50256, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_range": 0.02, "is_decoder": true, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_epsilon": 1e-05, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "model_type": "gpt2", "n_ctx": 1024, "n_embd": 768, "n_head": 12, "n_inner": null, "n_layer": 12, "n_positions": 1024, "no_repeat_ngram_size": 0, "num_beam_groups": 1, "num_beams": 1, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": 50256, "prefix": null, "problem_type": null, "pruned_heads": {}, "remove_invalid_values": false, "reorder_and_upcast_attn": false, "repetition_penalty": 1.0, "resid_pdrop": 0.1, "return_dict": true, "return_dict_in_generate": false, "scale_attn_by_inverse_layer_idx": false, "scale_attn_weights": true, "sep_token_id": null, "summary_activation": null, "summary_first_dropout": 0.1, "summary_proj_to_labels": true, "summary_type": "cls_index", "summary_use_proj": true, "task_specific_params": {"text-generation": {"do_sample": true, "max_length": 50}}, "temperature": 1.0, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "transformers_version": "4.15.0", "use_bfloat16": false, "use_cache": true, "vocab_size": 50257}, "decoder_start_token_id": 50256, "encoder": {"_name_or_path": "", "add_cross_attention": false, "architectures": ["ViTModel"], "attention_probs_dropout_prob": 0.0, "bad_words_ids": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.0, "hidden_size": 768, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "image_size": 224, "initializer_range": 0.02, "intermediate_size": 3072, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_eps": 1e-12, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "model_type": "vit", "no_repeat_ngram_size": 0, "num_attention_heads": 12, "num_beam_groups": 1, "num_beams": 1, "num_channels": 3, "num_hidden_layers": 12, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": null, "patch_size": 16, "prefix": null, "problem_type": null, "pruned_heads": {}, "qkv_bias": true, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "task_specific_params": null, "temperature": 1.0, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "transformers_version": "4.15.0", "use_bfloat16": false}, "eos_token_id": 50256, "is_encoder_decoder": true, "model_type": "vision-encoder-decoder", "pad_token_id": 50256, "tie_word_embeddings": false, "torch_dtype": "float32", "transformers_version": null}