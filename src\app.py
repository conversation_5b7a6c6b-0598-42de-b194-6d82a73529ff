"""
Streamlit Web Application for Image Captioning AI
"""

import streamlit as st
import time
from pathlib import Path
from PIL import Image
import plotly.express as px
import plotly.graph_objects as go
from streamlit_option_menu import option_menu

try:
    from .model import ImageCaptioningModel
    from .utils import ImageProcessor, display_image_info, load_example_images
    from .config import (
        APP_TITLE, APP_DESCRIPTION, STREAMLIT_CONFIG, CUSTOM_CSS,
        EXAMPLES_DIR
    )
except ImportError:
    # Handle relative imports when running directly
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent))

    from model import ImageCaptioningModel
    from utils import ImageProcessor, display_image_info, load_example_images
    from config import (
        APP_TITLE, APP_DESCRIPTION, STREAMLIT_CONFIG, CUSTOM_CSS,
        EXAMPLES_DIR
    )

# Configure Streamlit page
st.set_page_config(**STREAMLIT_CONFIG)

# Apply custom CSS
st.markdown(CUSTOM_CSS, unsafe_allow_html=True)


class ImageCaptioningApp:
    """Main application class for the Image Captioning interface"""

    def __init__(self):
        self.model = ImageCaptioningModel()
        self.image_processor = ImageProcessor()

        # Initialize session state
        if 'generated_captions' not in st.session_state:
            st.session_state.generated_captions = []
        if 'current_image' not in st.session_state:
            st.session_state.current_image = None
        if 'analysis_results' not in st.session_state:
            st.session_state.analysis_results = {}

    def render_header(self):
        """Render the application header"""
        st.markdown(f"""
        <div class="main-header">
            <h1>{APP_TITLE}</h1>
            <p style="font-size: 1.2em; margin: 0;">{APP_DESCRIPTION}</p>
        </div>
        """, unsafe_allow_html=True)

    def render_sidebar(self):
        """Render the sidebar with controls and information"""
        st.sidebar.title("🎛️ Controls")

        # Model information
        with st.sidebar.expander("🤖 Model Information", expanded=False):
            model_info = self.model.get_model_info()
            for key, value in model_info.items():
                st.write(f"**{key.replace('_', ' ').title()}:** {value}")

        # Generation parameters
        st.sidebar.subheader("⚙️ Generation Settings")

        num_captions = st.sidebar.slider(
            "Number of Captions",
            min_value=1,
            max_value=5,
            value=3,
            help="How many different captions to generate"
        )

        creativity = st.sidebar.slider(
            "Creativity Level",
            min_value=0.1,
            max_value=2.0,
            value=0.8,
            step=0.1,
            help="Higher values make captions more creative but less predictable"
        )

        return num_captions, creativity

    def render_image_upload(self):
        """Render the image upload interface"""
        st.subheader("📸 Upload Your Image")

        # Create tabs for different input methods
        tab1, tab2 = st.tabs(["📁 Upload File", "🖼️ Example Images"])

        with tab1:
            uploaded_file = st.file_uploader(
                "Choose an image file",
                type=['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'webp'],
                help="Upload an image to generate captions"
            )

            if uploaded_file is not None:
                if self.image_processor.validate_image(uploaded_file):
                    image = self.image_processor.load_image(uploaded_file)
                    if image:
                        st.session_state.current_image = image
                        return image
                else:
                    st.error("Please upload a valid image file.")

        with tab2:
            # Load example images
            example_images = load_example_images()

            if example_images:
                selected_example = st.selectbox(
                    "Choose an example image:",
                    options=[""] + example_images,
                    format_func=lambda x: Path(x).name if x else "Select an example..."
                )

                if selected_example:
                    image = self.image_processor.load_image(selected_example)
                    if image:
                        st.session_state.current_image = image
                        return image
            else:
                st.info("No example images found. Add some images to the 'examples' folder!")

        return None

    def render_image_display(self, image: Image.Image):
        """Display the uploaded image with analysis"""
        col1, col2 = st.columns([2, 1])

        with col1:
            st.image(
                image,
                caption="Uploaded Image",
                use_column_width=True,
                clamp=True
            )

        with col2:
            # Image analysis
            analysis = self.model.analyze_image(image)
            st.session_state.analysis_results = analysis

            if analysis:
                st.subheader("📊 Image Analysis")

                # Basic properties
                st.write(f"**Dimensions:** {analysis.get('dimensions', 'N/A')}")
                st.write(f"**Aspect Ratio:** {analysis.get('aspect_ratio', 'N/A')}")

                # Color and brightness info
                if 'brightness' in analysis:
                    brightness = analysis['brightness']
                    st.metric("Brightness", f"{brightness}%")

                if 'contrast' in analysis:
                    contrast = analysis['contrast']
                    st.metric("Contrast", f"{contrast}%")

                # Dominant colors visualization
                if 'dominant_colors' in analysis:
                    colors = analysis['dominant_colors']
                    fig = go.Figure(data=[
                        go.Bar(
                            x=['Red', 'Green', 'Blue'],
                            y=[colors['red'], colors['green'], colors['blue']],
                            marker_color=['red', 'green', 'blue']
                        )
                    ])
                    fig.update_layout(
                        title="Dominant Colors",
                        height=300,
                        showlegend=False
                    )
                    st.plotly_chart(fig, use_container_width=True)

    def render_caption_generation(self, image: Image.Image, num_captions: int, creativity: float):
        """Handle caption generation and display"""
        st.subheader("✨ Generated Captions")

        if st.button("🚀 Generate Captions", type="primary", use_container_width=True):
            with st.spinner("🧠 AI is analyzing your image..."):
                # Add progress bar
                progress_bar = st.progress(0)
                for i in range(100):
                    time.sleep(0.01)
                    progress_bar.progress(i + 1)

                # Generate captions
                captions = self.model.generate_captions(
                    image,
                    num_captions=num_captions,
                    creativity=creativity
                )

                st.session_state.generated_captions = captions
                progress_bar.empty()

        # Display generated captions
        if st.session_state.generated_captions:
            st.success(f"✅ Generated {len(st.session_state.generated_captions)} captions!")

            for i, result in enumerate(st.session_state.generated_captions):
                caption = result['caption']
                confidence = result['confidence']

                # Create caption display with confidence score
                st.markdown(f"""
                <div class="caption-box">
                    <h4>Caption {i+1}</h4>
                    <p style="font-size: 1.1em; margin: 0.5rem 0;">{caption}</p>
                    <span class="confidence-score">Confidence: {confidence}%</span>
                </div>
                """, unsafe_allow_html=True)

                # Copy button for each caption
                if st.button(f"📋 Copy Caption {i+1}", key=f"copy_{i}"):
                    st.write("Caption copied to clipboard!")
                    # Note: Actual clipboard functionality would require additional JS

    def render_statistics(self):
        """Render statistics and insights"""
        if st.session_state.generated_captions and st.session_state.analysis_results:
            st.subheader("📈 Insights & Statistics")

            col1, col2, col3 = st.columns(3)

            with col1:
                avg_confidence = sum(
                    result['confidence'] for result in st.session_state.generated_captions
                ) / len(st.session_state.generated_captions)
                st.metric("Average Confidence", f"{avg_confidence:.1f}%")

            with col2:
                caption_lengths = [
                    len(result['caption'].split())
                    for result in st.session_state.generated_captions
                ]
                avg_length = sum(caption_lengths) / len(caption_lengths)
                st.metric("Average Caption Length", f"{avg_length:.1f} words")

            with col3:
                brightness = st.session_state.analysis_results.get('brightness', 0)
                st.metric("Image Brightness", f"{brightness}%")

    def run(self):
        """Main application runner"""
        # Render header
        self.render_header()

        # Render sidebar and get parameters
        num_captions, creativity = self.render_sidebar()

        # Main content area
        image = self.render_image_upload()

        if image:
            # Display image and analysis
            self.render_image_display(image)

            # Caption generation
            self.render_caption_generation(image, num_captions, creativity)

            # Statistics
            self.render_statistics()

            # Display image info in sidebar
            display_image_info(image)

        else:
            # Show welcome message when no image is uploaded
            st.info("👆 Please upload an image or select an example to get started!")

            # Show some sample results or features
            st.subheader("🌟 Features")

            features = [
                "🎯 **State-of-the-art AI**: Uses Vision Transformer + GPT-2 architecture",
                "🎨 **Multiple Captions**: Generate up to 5 different captions per image",
                "📊 **Confidence Scoring**: See how confident the AI is about each caption",
                "🔧 **Customizable**: Adjust creativity and number of captions",
                "📱 **Responsive Design**: Works on desktop and mobile devices",
                "⚡ **Real-time Processing**: Fast caption generation"
            ]

            for feature in features:
                st.markdown(feature)


def main():
    """Main entry point for the Streamlit app"""
    app = ImageCaptioningApp()
    app.run()


if __name__ == "__main__":
    main()
