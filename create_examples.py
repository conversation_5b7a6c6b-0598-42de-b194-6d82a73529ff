#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create example images for testing the image captioning system
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_example_images():
    """Create sample images for testing"""

    # Create examples directory if it doesn't exist
    os.makedirs("examples", exist_ok=True)

    # Example 1: Simple landscape
    img1 = Image.new('RGB', (400, 300), color='skyblue')
    draw1 = ImageDraw.Draw(img1)

    # Draw sun
    draw1.ellipse([320, 30, 370, 80], fill='yellow')

    # Draw ground
    draw1.rectangle([0, 200, 400, 300], fill='green')

    # Draw mountains
    draw1.polygon([(0, 200), (100, 100), (200, 200)], fill='gray')
    draw1.polygon([(150, 200), (250, 120), (350, 200)], fill='#A9A9A9')  # dark gray

    img1.save("examples/landscape.jpg", "JPEG")
    print("Created examples/landscape.jpg")

    # Example 2: Simple house
    img2 = Image.new('RGB', (400, 300), color='lightblue')
    draw2 = ImageDraw.Draw(img2)

    # Draw ground
    draw2.rectangle([0, 250, 400, 300], fill='green')

    # Draw house
    draw2.rectangle([150, 150, 250, 250], fill='brown')

    # Draw roof
    draw2.polygon([(140, 150), (200, 100), (260, 150)], fill='red')

    # Draw door
    draw2.rectangle([180, 200, 220, 250], fill='#8B4513')  # dark brown

    # Draw windows
    draw2.rectangle([160, 170, 180, 190], fill='#ADD8E6')  # light blue
    draw2.rectangle([220, 170, 240, 190], fill='#ADD8E6')  # light blue

    img2.save("examples/house.jpg", "JPEG")
    print("Created examples/house.jpg")

    # Example 3: Abstract pattern
    img3 = Image.new('RGB', (400, 300), color='white')
    draw3 = ImageDraw.Draw(img3)

    # Draw colorful circles
    colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange']
    for i, color in enumerate(colors):
        x = (i % 3) * 120 + 60
        y = (i // 3) * 120 + 60
        draw3.ellipse([x-40, y-40, x+40, y+40], fill=color)

    img3.save("examples/abstract.jpg", "JPEG")
    print("Created examples/abstract.jpg")

    # Example 4: Simple car
    img4 = Image.new('RGB', (400, 300), color='lightgray')
    draw4 = ImageDraw.Draw(img4)

    # Draw road
    draw4.rectangle([0, 200, 400, 300], fill='#696969')  # dark gray

    # Draw car body
    draw4.rectangle([100, 150, 300, 200], fill='red')

    # Draw car roof
    draw4.rectangle([130, 120, 270, 150], fill='#8B0000')  # dark red

    # Draw wheels
    draw4.ellipse([120, 190, 150, 220], fill='black')
    draw4.ellipse([250, 190, 280, 220], fill='black')

    # Draw windows
    draw4.rectangle([140, 130, 190, 145], fill='#ADD8E6')  # light blue
    draw4.rectangle([210, 130, 260, 145], fill='#ADD8E6')  # light blue

    img4.save("examples/car.jpg", "JPEG")
    print("Created examples/car.jpg")

    # Example 5: Simple animal (cat)
    img5 = Image.new('RGB', (400, 300), color='lightgreen')
    draw5 = ImageDraw.Draw(img5)

    # Draw cat body
    draw5.ellipse([150, 180, 250, 220], fill='orange')

    # Draw cat head
    draw5.ellipse([170, 140, 230, 180], fill='orange')

    # Draw ears
    draw5.polygon([(180, 140), (190, 120), (200, 140)], fill='orange')
    draw5.polygon([(200, 140), (210, 120), (220, 140)], fill='orange')

    # Draw eyes
    draw5.ellipse([185, 155, 195, 165], fill='black')
    draw5.ellipse([205, 155, 215, 165], fill='black')

    # Draw tail
    draw5.ellipse([240, 190, 280, 210], fill='orange')

    img5.save("examples/cat.jpg", "JPEG")
    print("Created examples/cat.jpg")

    print("\n✅ All example images created successfully!")
    print("You can now test the image captioning system with these examples.")

if __name__ == "__main__":
    create_example_images()
