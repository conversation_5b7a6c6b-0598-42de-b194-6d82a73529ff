#!/usr/bin/env python3
"""
Main entry point for the Image Captioning AI application
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    """Main function to run the application"""
    try:
        print("🎨 Starting AI Image Captioning Studio...")
        print("📦 Checking dependencies...")

        # Check if streamlit is installed
        try:
            import streamlit
            print("✅ Streamlit found")
        except ImportError:
            print("❌ Streamlit not found. Installing dependencies...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)

        # Run the Streamlit app
        print("🚀 Launching web application...")
        print("🌐 Open your browser and go to: http://localhost:8501")

        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "src/app.py",
            "--server.port=8501",
            "--server.address=localhost"
        ], check=True)

    except subprocess.CalledProcessError as e:
        print(f"❌ Error running command: {e}")
        print("Please make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
