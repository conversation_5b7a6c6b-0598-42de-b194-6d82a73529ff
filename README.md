# 🎨 AI Image Captioning Studio

A modern, state-of-the-art image captioning AI that combines computer vision and natural language processing to generate compelling captions for your images.

![Python](https://img.shields.io/badge/python-v3.8+-blue.svg)
![PyTorch](https://img.shields.io/badge/PyTorch-v2.0+-red.svg)
![Transformers](https://img.shields.io/badge/🤗%20Transformers-v4.35+-yellow.svg)
![Streamlit](https://img.shields.io/badge/Streamlit-v1.28+-green.svg)

## ✨ Features

- 🎯 **State-of-the-art AI**: Uses Vision Transformer (ViT) + GPT-2 architecture
- 🎨 **Multiple Captions**: Generate up to 5 different captions per image
- 📊 **Confidence Scoring**: See how confident the AI is about each caption
- 🔧 **Customizable Parameters**: Adjust creativity level and number of captions
- 📱 **Responsive Web Interface**: Beautiful, mobile-friendly Streamlit app
- ⚡ **Real-time Processing**: Fast caption generation with progress tracking
- 📈 **Image Analysis**: Detailed analysis of image properties and colors
- 🖼️ **Multiple Input Methods**: Upload files or use example images

## 🏗️ Architecture

The system uses a modern encoder-decoder architecture:

- **Vision Encoder**: Vision Transformer (ViT) for extracting rich visual features
- **Language Decoder**: GPT-2 for generating natural language captions
- **Pre-trained Model**: `nlpconnect/vit-gpt2-image-captioning` from Hugging Face

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd image-captioning
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   streamlit run main.py
   ```

4. **Open your browser** and navigate to `http://localhost:8501`

## 📖 Usage

### Web Interface

1. **Upload an Image**: Use the file uploader or select from example images
2. **Adjust Settings**: Configure the number of captions and creativity level in the sidebar
3. **Generate Captions**: Click the "Generate Captions" button
4. **View Results**: See multiple captions with confidence scores and image analysis

### Programmatic Usage

```python
from src.model import ImageCaptioningModel
from PIL import Image

# Initialize the model
model = ImageCaptioningModel()

# Load an image
image = Image.open("path/to/your/image.jpg")

# Generate captions
captions = model.generate_captions(image, num_captions=3, creativity=0.8)

for result in captions:
    print(f"Caption: {result['caption']}")
    print(f"Confidence: {result['confidence']}%")
```

## 🎛️ Configuration

Key parameters can be adjusted in `src/config.py`:

- **Model Settings**: Model name, device selection
- **Generation Parameters**: Max length, beam search, temperature
- **UI Configuration**: Styling, layout options
- **Image Processing**: Supported formats, max image size

## 📁 Project Structure

```
image-captioning/
├── src/
│   ├── __init__.py
│   ├── app.py              # Streamlit web application
│   ├── model.py            # Core image captioning model
│   ├── utils.py            # Utility functions
│   └── config.py           # Configuration settings
├── tests/
│   ├── __init__.py
│   └── test_model.py       # Unit tests
├── examples/               # Example images
├── requirements.txt        # Python dependencies
├── main.py                # Main entry point
└── README.md              # This file
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest tests/

# Run with coverage
pytest tests/ --cov=src

# Run specific test file
pytest tests/test_model.py -v
```

## 🔧 Development

### Adding New Features

1. **Model Enhancements**: Modify `src/model.py` to add new model capabilities
2. **UI Components**: Update `src/app.py` to add new interface elements
3. **Utilities**: Add helper functions to `src/utils.py`
4. **Configuration**: Update `src/config.py` for new settings

### Code Quality

```bash
# Format code
black src/ tests/

# Check style
flake8 src/ tests/

# Sort imports
isort src/ tests/
```

## 📊 Model Performance

The Vision Transformer + GPT-2 model provides:

- **High Accuracy**: State-of-the-art performance on standard benchmarks
- **Fast Inference**: Optimized for real-time caption generation
- **Robust**: Works well across diverse image types and domains
- **Scalable**: Efficient memory usage and GPU acceleration support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [Hugging Face](https://huggingface.co/) for the pre-trained models and transformers library
- [Streamlit](https://streamlit.io/) for the amazing web framework
- [PyTorch](https://pytorch.org/) for the deep learning foundation
- The research community for advancing vision-language models

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) page
2. Create a new issue with detailed information
3. Include error messages and system information

---

**Made with ❤️ and AI**
