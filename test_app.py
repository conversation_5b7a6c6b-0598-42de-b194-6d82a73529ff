#!/usr/bin/env python3
"""
Simple test version of the Image Captioning AI
"""

import streamlit as st
import sys
from pathlib import Path

# Add src to path
sys.path.append('src')

def main():
    st.set_page_config(
        page_title="AI Image Captioning",
        page_icon="🎨",
        layout="wide"
    )
    
    st.title("🎨 AI Image Captioning Studio")
    st.write("Welcome to the AI Image Captioning application!")
    
    # Test basic functionality
    st.subheader("📸 Upload Your Image")
    
    uploaded_file = st.file_uploader(
        "Choose an image file",
        type=['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'webp'],
        help="Upload an image to generate captions"
    )
    
    if uploaded_file is not None:
        from PIL import Image
        
        # Load and display image
        image = Image.open(uploaded_file)
        st.image(image, caption="Uploaded Image", use_column_width=True)
        
        # Test model loading
        if st.button("🚀 Test Model Loading", type="primary"):
            with st.spinner("Loading AI model..."):
                try:
                    from model import ImageCaptioningModel
                    model = ImageCaptioningModel()
                    st.success("✅ Model loaded successfully!")
                    
                    # Test caption generation
                    with st.spinner("Generating captions..."):
                        captions = model.generate_captions(image, num_captions=1)
                        if captions:
                            st.success(f"✅ Generated caption: {captions[0]['caption']}")
                        else:
                            st.warning("⚠️ No captions generated")
                            
                except Exception as e:
                    st.error(f"❌ Error: {e}")
                    st.write("This is expected on first run as the model needs to download.")
    
    else:
        st.info("👆 Please upload an image to get started!")
        
        # Show features
        st.subheader("🌟 Features")
        features = [
            "🎯 **State-of-the-art AI**: Uses Vision Transformer + GPT-2",
            "🎨 **Multiple Captions**: Generate several captions per image",
            "📊 **Confidence Scoring**: See AI confidence levels",
            "⚡ **Real-time Processing**: Fast caption generation"
        ]
        
        for feature in features:
            st.markdown(feature)

if __name__ == "__main__":
    main()
