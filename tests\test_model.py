"""
Unit tests for the Image Captioning Model
"""

import pytest
import sys
from pathlib import Path
from PIL import Image
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.model import ImageCaptioningModel
from src.utils import ImageProcessor, TextProcessor


class TestImageProcessor:
    """Test cases for ImageProcessor"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.processor = ImageProcessor()
        
        # Create a test image
        self.test_image = Image.new('RGB', (100, 100), color='red')
    
    def test_validate_image(self):
        """Test image validation"""
        # Mock file object
        class MockFile:
            def __init__(self, name):
                self.name = name
        
        # Valid formats
        assert self.processor.validate_image(MockFile("test.jpg"))
        assert self.processor.validate_image(MockFile("test.png"))
        assert self.processor.validate_image(MockFile("test.jpeg"))
        
        # Invalid formats
        assert not self.processor.validate_image(MockFile("test.txt"))
        assert not self.processor.validate_image(MockFile("test.pdf"))
        assert not self.processor.validate_image(None)
    
    def test_preprocess_image(self):
        """Test image preprocessing"""
        processed = self.processor.preprocess_image(self.test_image, (50, 50))
        
        assert processed.size == (50, 50)
        assert processed.mode == 'RGB'
    
    def test_enhance_image(self):
        """Test image enhancement"""
        enhanced = self.processor.enhance_image(
            self.test_image, 
            brightness=1.2, 
            contrast=1.1, 
            saturation=0.9
        )
        
        assert enhanced.size == self.test_image.size
        assert enhanced.mode == self.test_image.mode


class TestTextProcessor:
    """Test cases for TextProcessor"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.processor = TextProcessor()
    
    def test_clean_caption(self):
        """Test caption cleaning"""
        # Test basic cleaning
        assert self.processor.clean_caption("hello world") == "Hello world."
        assert self.processor.clean_caption("Hello world!") == "Hello world!"
        assert self.processor.clean_caption("hello world.") == "Hello world."
        
        # Test special token removal
        assert self.processor.clean_caption("hello<|endoftext|>") == "Hello."
        
        # Test empty string
        assert self.processor.clean_caption("") == ""
    
    def test_format_captions(self):
        """Test multiple caption formatting"""
        captions = ["hello world", "another caption", "third one!"]
        formatted = self.processor.format_captions(captions)
        
        assert len(formatted) == 3
        assert all(cap[0].isupper() for cap in formatted if cap)
        assert all(cap.endswith(('.', '!', '?')) for cap in formatted if cap)


class TestImageCaptioningModel:
    """Test cases for ImageCaptioningModel"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.model = ImageCaptioningModel()
        self.test_image = Image.new('RGB', (224, 224), color='blue')
    
    def test_model_initialization(self):
        """Test model initialization"""
        assert self.model.model is None
        assert self.model.processor is None
        assert self.model.tokenizer is None
        assert self.model.device in ['cpu', 'cuda']
    
    def test_get_model_info(self):
        """Test model information retrieval"""
        info = self.model.get_model_info()
        
        assert 'model_name' in info
        assert 'architecture' in info
        assert 'device' in info
        assert info['encoder'] == 'ViT (Vision Transformer)'
        assert info['decoder'] == 'GPT-2'
    
    def test_analyze_image(self):
        """Test image analysis"""
        analysis = self.model.analyze_image(self.test_image)
        
        assert 'dimensions' in analysis
        assert 'aspect_ratio' in analysis
        assert 'mode' in analysis
        assert analysis['dimensions'] == "224 × 224"
        assert analysis['aspect_ratio'] == 1.0
        assert analysis['mode'] == 'RGB'
    
    @pytest.mark.slow
    def test_generate_captions_mock(self):
        """Test caption generation with mocked model"""
        # This test would require the actual model to be loaded
        # For now, we'll test the method exists and handles errors gracefully
        
        # Mock the model loading to fail gracefully
        captions = self.model.generate_captions(self.test_image, num_captions=1)
        
        # Should return empty list if model not loaded
        assert isinstance(captions, list)


if __name__ == "__main__":
    pytest.main([__file__])
