"""
Configuration settings for the Image Captioning AI
"""

import os
from pathlib import Path

# Model Configuration
MODEL_NAME = "nlpconnect/vit-gpt2-image-captioning"
DEVICE = "cuda" if os.getenv("CUDA_AVAILABLE", "false").lower() == "true" else "cpu"

# Image Processing
MAX_IMAGE_SIZE = (512, 512)
SUPPORTED_FORMATS = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]

# Generation Parameters
MAX_LENGTH = 50
MIN_LENGTH = 10
NUM_BEAMS = 5
NUM_RETURN_SEQUENCES = 3
TEMPERATURE = 0.8
DO_SAMPLE = True
EARLY_STOPPING = True

# UI Configuration
APP_TITLE = "🎨 AI Image Captioning Studio"
APP_DESCRIPTION = """
Transform your images into compelling stories with our state-of-the-art AI model.
Upload an image and watch as our Vision Transformer + GPT-2 model generates 
creative and accurate captions in real-time.
"""

# Paths
PROJECT_ROOT = Path(__file__).parent.parent
EXAMPLES_DIR = PROJECT_ROOT / "examples"
CACHE_DIR = PROJECT_ROOT / ".cache"

# Streamlit Configuration
STREAMLIT_CONFIG = {
    "page_title": "AI Image Captioning",
    "page_icon": "🎨",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# Model Cache
CACHE_DIR.mkdir(exist_ok=True)
MODEL_CACHE_PATH = CACHE_DIR / "model_cache"

# Styling
CUSTOM_CSS = """
<style>
    .main-header {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .caption-box {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        margin: 1rem 0;
    }
    
    .confidence-score {
        background: linear-gradient(90deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        display: inline-block;
        font-weight: bold;
    }
    
    .upload-area {
        border: 2px dashed #667eea;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        background: #f8f9fa;
    }
</style>
"""
