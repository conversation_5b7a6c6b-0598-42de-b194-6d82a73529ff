"""
Image Captioning Model using Vision Transformer and GPT-2
"""

import logging
from typing import List, Dict, Optional, Tuple
import warnings

import torch
from transformers import (
    VisionEncoderDecoderModel,
    ViTImageProcessor,
    AutoTokenizer,
    GenerationConfig
)
from PIL import Image
import streamlit as st

try:
    from .config import (
        MODEL_NAME, DEVICE, MAX_LENGTH, MIN_LENGTH, NUM_BEAMS,
        NUM_RETURN_SEQUENCES, TEMPERATURE, DO_SAMPLE, EARLY_STOPPING,
        MODEL_CACHE_PATH
    )
    from .utils import ImageProcessor, TextProcessor
except ImportError:
    from config import (
        MODEL_NAME, DEVICE, MAX_LENGTH, MIN_LENGTH, NUM_BEAMS,
        NUM_RETURN_SEQUENCES, TEMPERATURE, DO_SAMPLE, EARLY_STOPPING,
        MODEL_CACHE_PATH
    )
    from utils import ImageProcessor, TextProcessor

# Suppress warnings
warnings.filterwarnings("ignore")
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageCaptioningModel:
    """
    A wrapper class for the Vision Transformer + GPT-2 image captioning model
    """

    def __init__(self):
        self.model = None
        self.processor = None
        self.tokenizer = None
        self.device = DEVICE
        self.image_processor = ImageProcessor()
        self.text_processor = TextProcessor()

    @st.cache_resource
    def load_model(_self):
        """Load the pre-trained model, processor, and tokenizer"""
        try:
            logger.info(f"Loading model: {MODEL_NAME}")
            logger.info(f"Using device: {_self.device}")

            # Load model components
            _self.model = VisionEncoderDecoderModel.from_pretrained(
                MODEL_NAME,
                cache_dir=MODEL_CACHE_PATH
            )
            _self.processor = ViTImageProcessor.from_pretrained(
                MODEL_NAME,
                cache_dir=MODEL_CACHE_PATH
            )
            _self.tokenizer = AutoTokenizer.from_pretrained(
                MODEL_NAME,
                cache_dir=MODEL_CACHE_PATH
            )

            # Move model to device
            _self.model.to(_self.device)
            _self.model.eval()

            # Set generation config
            _self.model.config.decoder_start_token_id = _self.tokenizer.cls_token_id
            _self.model.config.pad_token_id = _self.tokenizer.pad_token_id

            logger.info("Model loaded successfully!")
            return True

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            st.error(f"Failed to load model: {e}")
            return False

    def generate_captions(self, image: Image.Image,
                         num_captions: int = NUM_RETURN_SEQUENCES,
                         creativity: float = TEMPERATURE) -> List[Dict[str, any]]:
        """
        Generate captions for the input image

        Args:
            image: PIL Image object
            num_captions: Number of captions to generate
            creativity: Temperature for generation (higher = more creative)

        Returns:
            List of dictionaries containing caption and confidence score
        """
        if not self.model:
            if not self.load_model():
                return []

        try:
            # Preprocess image
            processed_image = self.image_processor.preprocess_image(image)

            # Process image for model input
            pixel_values = self.processor(
                processed_image,
                return_tensors="pt"
            ).pixel_values.to(self.device)

            # Generate captions
            with torch.no_grad():
                generation_config = GenerationConfig(
                    max_length=MAX_LENGTH,
                    min_length=MIN_LENGTH,
                    num_beams=NUM_BEAMS,
                    num_return_sequences=min(num_captions, NUM_RETURN_SEQUENCES),
                    temperature=creativity,
                    do_sample=DO_SAMPLE,
                    early_stopping=EARLY_STOPPING,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    decoder_start_token_id=self.tokenizer.cls_token_id,
                    return_dict_in_generate=True,
                    output_scores=True
                )

                outputs = self.model.generate(
                    pixel_values,
                    generation_config=generation_config
                )

            # Decode captions
            captions = self.tokenizer.batch_decode(
                outputs.sequences,
                skip_special_tokens=True
            )

            # Clean and format captions
            cleaned_captions = self.text_processor.format_captions(captions)

            # Calculate confidence scores
            results = []
            for i, caption in enumerate(cleaned_captions):
                # Simple confidence calculation based on sequence scores
                if hasattr(outputs, 'sequences_scores'):
                    confidence = float(torch.exp(outputs.sequences_scores[i]) * 100)
                else:
                    confidence = 85.0 + (i * -5)  # Fallback scoring

                results.append({
                    'caption': caption,
                    'confidence': round(max(0, min(100, confidence)), 1)
                })

            return results

        except Exception as e:
            logger.error(f"Error generating captions: {e}")
            st.error(f"Failed to generate captions: {e}")
            return []

    def analyze_image(self, image: Image.Image) -> Dict[str, any]:
        """
        Perform comprehensive image analysis

        Args:
            image: PIL Image object

        Returns:
            Dictionary containing analysis results
        """
        try:
            # Basic image properties
            analysis = {
                'dimensions': f"{image.width} × {image.height}",
                'aspect_ratio': round(image.width / image.height, 2),
                'mode': image.mode,
                'has_transparency': image.mode in ('RGBA', 'LA') or 'transparency' in image.info
            }

            # Color analysis
            if image.mode == 'RGB':
                # Convert to numpy array for analysis
                import numpy as np
                img_array = np.array(image)

                # Calculate average colors
                avg_colors = np.mean(img_array, axis=(0, 1))
                analysis['dominant_colors'] = {
                    'red': int(avg_colors[0]),
                    'green': int(avg_colors[1]),
                    'blue': int(avg_colors[2])
                }

                # Brightness analysis
                brightness = np.mean(img_array)
                analysis['brightness'] = round(brightness / 255 * 100, 1)

                # Contrast analysis (standard deviation)
                contrast = np.std(img_array)
                analysis['contrast'] = round(contrast / 255 * 100, 1)

            return analysis

        except Exception as e:
            logger.error(f"Error analyzing image: {e}")
            return {}

    def get_model_info(self) -> Dict[str, str]:
        """Get information about the loaded model"""
        return {
            'model_name': MODEL_NAME,
            'architecture': 'Vision Transformer + GPT-2',
            'encoder': 'ViT (Vision Transformer)',
            'decoder': 'GPT-2',
            'device': self.device,
            'parameters': 'Approximately 1B parameters'
        }
