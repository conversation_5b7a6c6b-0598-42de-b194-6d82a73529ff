"""
Utility functions for image processing and model operations
"""

import io
import logging
from pathlib import Path
from typing import List, Tuple, Union, Optional

import cv2
import numpy as np
import torch
from PIL import Image, ImageEnhance, ImageOps
import streamlit as st

try:
    from .config import MAX_IMAGE_SIZE, SUPPORTED_FORMATS
except ImportError:
    from config import MAX_IMAGE_SIZE, SUPPORTED_FORMATS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageProcessor:
    """Handles image preprocessing and enhancement"""

    @staticmethod
    def validate_image(image_file) -> bool:
        """Validate if the uploaded file is a supported image format"""
        if image_file is None:
            return False

        file_extension = Path(image_file.name).suffix.lower()
        return file_extension in SUPPORTED_FORMATS

    @staticmethod
    def load_image(image_source: Union[str, bytes, Image.Image]) -> Optional[Image.Image]:
        """Load image from various sources"""
        try:
            if isinstance(image_source, str):
                # File path
                return Image.open(image_source).convert('RGB')
            elif isinstance(image_source, bytes):
                # Bytes data
                return Image.open(io.BytesIO(image_source)).convert('RGB')
            elif hasattr(image_source, 'read'):
                # File-like object
                return Image.open(image_source).convert('RGB')
            elif isinstance(image_source, Image.Image):
                # PIL Image
                return image_source.convert('RGB')
            else:
                logger.error(f"Unsupported image source type: {type(image_source)}")
                return None
        except Exception as e:
            logger.error(f"Error loading image: {e}")
            return None

    @staticmethod
    def preprocess_image(image: Image.Image, target_size: Tuple[int, int] = MAX_IMAGE_SIZE) -> Image.Image:
        """Preprocess image for model input"""
        try:
            # Resize while maintaining aspect ratio
            image.thumbnail(target_size, Image.Resampling.LANCZOS)

            # Create a new image with the target size and paste the resized image
            processed_image = Image.new('RGB', target_size, (255, 255, 255))

            # Calculate position to center the image
            x = (target_size[0] - image.width) // 2
            y = (target_size[1] - image.height) // 2

            processed_image.paste(image, (x, y))

            return processed_image
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            return image

    @staticmethod
    def enhance_image(image: Image.Image, brightness: float = 1.0,
                     contrast: float = 1.0, saturation: float = 1.0) -> Image.Image:
        """Apply image enhancements"""
        try:
            # Brightness
            if brightness != 1.0:
                enhancer = ImageEnhance.Brightness(image)
                image = enhancer.enhance(brightness)

            # Contrast
            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(contrast)

            # Saturation
            if saturation != 1.0:
                enhancer = ImageEnhance.Color(image)
                image = enhancer.enhance(saturation)

            return image
        except Exception as e:
            logger.error(f"Error enhancing image: {e}")
            return image


class TextProcessor:
    """Handles text processing and caption formatting"""

    @staticmethod
    def clean_caption(caption: str) -> str:
        """Clean and format generated caption"""
        # Remove special tokens
        caption = caption.replace('<|endoftext|>', '').strip()

        # Capitalize first letter
        if caption:
            caption = caption[0].upper() + caption[1:]

        # Ensure proper punctuation
        if caption and not caption.endswith(('.', '!', '?')):
            caption += '.'

        return caption

    @staticmethod
    def format_captions(captions: List[str]) -> List[str]:
        """Format multiple captions"""
        return [TextProcessor.clean_caption(caption) for caption in captions]

    @staticmethod
    def calculate_confidence_score(logits: torch.Tensor) -> float:
        """Calculate confidence score from model logits"""
        try:
            # Convert logits to probabilities
            probs = torch.softmax(logits, dim=-1)

            # Calculate average confidence (max probability per token)
            max_probs = torch.max(probs, dim=-1)[0]
            confidence = torch.mean(max_probs).item()

            return round(confidence * 100, 2)
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.0


def create_download_link(image: Image.Image, filename: str = "captioned_image.png") -> str:
    """Create a download link for the processed image"""
    try:
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        buffer.seek(0)

        return st.download_button(
            label="📥 Download Image",
            data=buffer.getvalue(),
            file_name=filename,
            mime="image/png"
        )
    except Exception as e:
        logger.error(f"Error creating download link: {e}")
        return None


def display_image_info(image: Image.Image) -> None:
    """Display image information in the sidebar"""
    st.sidebar.subheader("📊 Image Information")
    st.sidebar.write(f"**Dimensions:** {image.width} × {image.height}")
    st.sidebar.write(f"**Mode:** {image.mode}")
    st.sidebar.write(f"**Format:** {getattr(image, 'format', 'Unknown')}")

    # Calculate file size approximation
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    size_kb = len(buffer.getvalue()) / 1024
    st.sidebar.write(f"**Size:** {size_kb:.1f} KB")


@st.cache_data
def load_example_images() -> List[str]:
    """Load example images from the examples directory"""
    examples_dir = Path("examples")
    if not examples_dir.exists():
        return []

    example_files = []
    for ext in ['.jpg', '.jpeg', '.png']:
        example_files.extend(examples_dir.glob(f"*{ext}"))

    return [str(f) for f in example_files[:6]]  # Limit to 6 examples
