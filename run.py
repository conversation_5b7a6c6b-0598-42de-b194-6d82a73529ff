#!/usr/bin/env python3
"""
Simple runner script for the Image Captioning AI
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "streamlit", "torch", "torchvision", "transformers", 
            "pillow", "numpy", "plotly", "opencv-python"
        ], check=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def run_app():
    """Run the Streamlit application"""
    print("🚀 Starting Image Captioning AI...")
    print("🌐 Open your browser and go to: http://localhost:8501")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "src/app.py",
            "--server.port=8501",
            "--server.address=localhost"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running application: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        return True
    
    return True

def main():
    """Main function"""
    print("🎨 AI Image Captioning Studio")
    print("=" * 40)
    
    # Check if streamlit is available
    try:
        import streamlit
        print("✅ Streamlit found")
    except ImportError:
        print("❌ Streamlit not found")
        if not install_dependencies():
            sys.exit(1)
    
    # Run the application
    if not run_app():
        sys.exit(1)

if __name__ == "__main__":
    main()
